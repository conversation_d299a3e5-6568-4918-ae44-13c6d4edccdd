/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_workflow.h"

#include <atomic>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <future>
#include <iostream>
#include <map>
#include <thread>
#include <variant>

#include <aubo-base/log.h>
#include <nlohmann/json.hpp>
#include <taskflow/taskflow.hpp>

#include "../devices/coffee_machine/coffee_machine.h"
#include "../devices/cup_dispenser/cup_dispenser.h"
#include "../devices/device_manager.h"
#include "../devices/milk_container_cleaner/milk_container_cleaner.h"
#include "../devices/robotic_arms/main_robotic_arm.h"
#include "../devices/robotic_arms/sub_robotic_arm.h"

namespace aubo {

using json = nlohmann::json;

class CoffeeWorkflowEngine::Impl {
public:
    Impl(std::shared_ptr<DeviceManager> device_manager)
        : device_manager_(device_manager),
          is_executing_(false), stop_requested_(false) {
        // 从设备管理器获取各个设备
        main_robotic_arm_ = device_manager_->get_main_robotic_arm();
        sub_robotic_arm_ = device_manager_->get_sub_robotic_arm();
        cup_dispenser_ = device_manager_->get_cup_dispenser();
        coffee_machine_ = device_manager_->get_coffee_machine();
        milk_container_cleaner_ = device_manager_->get_milk_container_cleaner();
    }

    bool load_workflows_from_directory(const std::string& workflow_directory) {
        LOG_INFO("[WorkflowEngine] 从目录加载工作流: {}", workflow_directory);

        workflows_.clear();
        int loaded_count = 0;

        try {
            // 这里应该遍历目录中的所有JSON文件
            // 为了简化，我们手动加载已知的工作流文件
            std::vector<std::string> workflow_files = {
                "americano.json",
                "latte_heart.json",
                "latte_leaf.json",
                "latte_tulip.json",
                "latte_swan.json",
                "cappuccino_heart.json"
            };

            for (const auto& filename : workflow_files) {
                std::string filepath = workflow_directory + "/" + filename;
                if (load_single_workflow(filepath)) {
                    loaded_count++;
                }
            }

            LOG_INFO("[WorkflowEngine] 从目录成功加载 {} 个工作流", loaded_count);
            return loaded_count > 0;

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 从目录加载工作流失败: {}", e.what());
            return false;
        }
    }

    bool load_single_workflow(const std::string& workflow_file_path) {
        try {
            std::ifstream file(workflow_file_path);
            if (!file.is_open()) {
                LOG_WARN("[WorkflowEngine] 无法打开工作流文件: {}", workflow_file_path);
                return false;
            }

            json workflow_json;
            file >> workflow_json;

            if (workflow_json.contains("workflow")) {
                const auto& workflow_data = workflow_json["workflow"];

                // 从文件名提取工作流ID
                std::string filename = workflow_file_path.substr(workflow_file_path.find_last_of("/") + 1);
                std::string workflow_id = filename.substr(0, filename.find_last_of("."));

                CoffeeWorkflow workflow = parse_single_workflow(workflow_data);
                workflows_[workflow_id] = workflow;

                LOG_INFO("[WorkflowEngine] 加载单个工作流: {} - {}", workflow_id, workflow.description);
                return true;
            }

            return false;

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 加载单个工作流失败: {} - {}", workflow_file_path, e.what());
            return false;
        }
    }

    std::vector<std::string> get_available_workflows() const {
        std::vector<std::string> names;
        for (const auto& [name, workflow] : workflows_) {
            names.push_back(name);
        }
        return names;
    }

    CoffeeWorkflow get_workflow(const std::string& workflow_name) const {
        auto it = workflows_.find(workflow_name);
        if (it != workflows_.end()) {
            return it->second;
        }
        return CoffeeWorkflow();
    }

    bool execute_workflow(const std::string& workflow_name) {
        auto it = workflows_.find(workflow_name);
        if (it == workflows_.end()) {
            LOG_ERROR("[WorkflowEngine] 工作流不存在: {}", workflow_name);
            return false;
        }
        return execute_workflow(it->second);
    }

    bool execute_workflow(const CoffeeWorkflow& workflow) {
        if (is_executing_) {
            LOG_ERROR("[WorkflowEngine] 已有工作流正在执行");
            return false;
        }

        LOG_INFO("[WorkflowEngine] 开始执行工作流: {} - {}", workflow.name, workflow.description);

        is_executing_ = true;
        current_step_ = "";
        stop_requested_ = false;

        bool success = true;

        try {
            // 执行根步骤组
            std::string step_name = std::visit([](const auto& item) -> std::string {
                return item.name;
            }, *workflow.steps);

            current_step_ = step_name;
            LOG_INFO("[WorkflowEngine] 执行工作流: {} - {}", workflow.name, step_name);

            if (!execute_workflow_item(*workflow.steps)) {
                LOG_ERROR("[WorkflowEngine] 工作流执行失败: {}", step_name);
                success = false;
            }
        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 工作流执行异常: {}", e.what());
            success = false;
        }

        is_executing_ = false;
        current_step_ = "";

        if (stop_requested_) {
            LOG_WARN("[WorkflowEngine] 工作流执行被用户停止");
            return false;
        }

        if (success) {
            LOG_INFO("[WorkflowEngine] 工作流执行完成: {}", workflow.name);
        } else {
            LOG_ERROR("[WorkflowEngine] 工作流执行失败: {}", workflow.name);
        }

        return success;
    }

    bool stop_current_workflow() {
        if (!is_executing_) {
            LOG_WARN("[WorkflowEngine] 没有正在执行的工作流");
            return false;
        }

        LOG_INFO("[WorkflowEngine] 请求停止当前工作流");
        stop_requested_ = true;
        return true;
    }

    bool is_executing() const {
        return is_executing_;
    }

    std::string get_current_step() const {
        return current_step_;
    }

private:
    // 辅助函数：解析拉花类型
    LatteArtType parse_latte_art_type(const WorkflowAction& action) {
        // 从动作参数中获取
        if (action.parameters.has("art_type")) {
            std::string art_type_str = action.parameters.get<std::string>("art_type");
            return string_to_latte_art_type(art_type_str);
        }

        return LatteArtType::NONE;
    }

    // 辅助函数：字符串转拉花类型
    LatteArtType string_to_latte_art_type(const std::string& art_type_str) {
        if (art_type_str == "heart") return LatteArtType::HEART;
        else if (art_type_str == "leaf") return LatteArtType::LEAF;
        else if (art_type_str == "tulip") return LatteArtType::TULIP;
        else if (art_type_str == "swan") return LatteArtType::SWAN;
        else if (art_type_str == "none") return LatteArtType::NONE;
        return LatteArtType::NONE;
    }

    CoffeeWorkflow parse_single_workflow(const json& workflow_json) {
        CoffeeWorkflow workflow;

        if (workflow_json.contains("name")) {
            workflow.name = workflow_json["name"];
        }
        if (workflow_json.contains("description")) {
            workflow.description = workflow_json["description"];
        }
        if (workflow_json.contains("coffee_type")) {
            workflow.coffee_type = workflow_json["coffee_type"];
        }

        // 解析 steps 作为根执行组
        if (workflow_json.contains("steps")) {
            workflow.steps = parse_workflow_item(workflow_json["steps"]);
        }

        return workflow;
    }

    std::shared_ptr<WorkflowItem> parse_workflow_item(const json& item_json) {
        // 根据 type 字段确定类型
        std::string type_str = "";  // 默认类型
        if (item_json.contains("type")) {
            type_str = item_json["type"];
        } else {
            if (item_json.contains("items")) {
                type_str = "group";
            } else {
                LOG_ERROR("[WorkflowEngine] 无法确定工作流项类型");
                return nullptr;
            }
        }

        if (type_str == "action") {
            WorkflowAction action;
            if (item_json.contains("name")) {
                action.name = item_json["name"];
            }

            if (item_json.contains("executor")) {
                action.executor = string_to_executor_type(item_json["executor"]);
            } else {
                LOG_ERROR("[WorkflowEngine] 动作项缺少执行器");
                return nullptr;
            }

            if (item_json.contains("operation")) {
                action.operation = item_json["operation"];
            } else {
                LOG_ERROR("[WorkflowEngine] 动作项缺少操作名称");
                return nullptr;
            }

            if (item_json.contains("parameters")) {
                for (const auto& [key, value] : item_json["parameters"].items()) {
                    if (value.is_string()) {
                        action.parameters.set(key, value.get<std::string>());
                    } else if (value.is_number_integer()) {
                        action.parameters.set(key, value.get<int>());
                    } else if (value.is_number_float()) {
                        action.parameters.set(key, value.get<double>());
                    } else if (value.is_boolean()) {
                        action.parameters.set(key, value.get<bool>());
                    }
                }
            }
            return std::make_shared<WorkflowItem>(action);

        } else if (type_str == "wait") {
            WorkflowWait wait;

            if (item_json.contains("name")) {
                wait.name = item_json["name"];
            }

            if (item_json.contains("duration")) {
                wait.duration = item_json["duration"];
            } else {
                LOG_ERROR("[WorkflowEngine] 等待项缺少持续时间");
                return nullptr;
            }

            return std::make_shared<WorkflowItem>(wait);

        } else if (type_str == "group") {
            WorkflowGroup group;
            if (item_json.contains("name")) {
                group.name = item_json["name"];
            } else {
                LOG_ERROR("[WorkflowEngine] 组项缺少名称");
                return nullptr;
            }

            if (item_json.contains("mode")) {
                group.mode = string_to_mode_type(item_json["mode"]);
            } else {
                LOG_ERROR("[WorkflowEngine] 组项缺少执行模式");
                return nullptr;
            }

            // 解析子项列表
            if (item_json.contains("items")) {
                for (const auto& sub_item_json : item_json["items"]) {
                    auto sub_item = parse_workflow_item(sub_item_json);
                    if (sub_item) {
                        group.items.push_back(*sub_item);
                    }
                }
            } else {
                LOG_WARN("[WorkflowEngine] 组项缺少子项列表");
                return nullptr;
            }

            if (!group.items.empty()) {
                return std::make_shared<WorkflowItem>(group);
            }

        }

        return nullptr;
    }





    bool execute_items_sequential(const std::vector<WorkflowItem>& items) {
        for (const auto& item : items) {
            if (stop_requested_) return false;

            if (!execute_workflow_item(item)) {
                return false;
            }
        }
        return true;
    }

    bool execute_items_parallel(const std::vector<WorkflowItem>& items) {
        if (items.empty()) return true;

        tf::Executor executor;
        tf::Taskflow taskflow;

        std::vector<bool> results(items.size(), false);

        for (size_t i = 0; i < items.size(); ++i) {
            auto task = taskflow.emplace([this, &items, &results, i]() {
                if (!stop_requested_) {
                    results[i] = execute_workflow_item(items[i]);
                }
            });
            std::string task_name = std::visit([](const auto& item) -> std::string {
                return item.name.empty() ? "unnamed_task" : item.name;
            }, items[i]);
            task.name(task_name);
        }

        executor.run(taskflow).wait();

        // 检查所有任务是否成功
        for (bool result : results) {
            if (!result) return false;
        }

        return true;
    }

    bool execute_workflow_item(const WorkflowItem& item) {
        return std::visit([this](const auto& concrete_item) -> bool {
            using T = std::decay_t<decltype(concrete_item)>;
            if constexpr (std::is_same_v<T, WorkflowAction>) {
                return execute_single_action(concrete_item);
            } else if constexpr (std::is_same_v<T, WorkflowWait>) {
                return execute_wait_action(concrete_item);
            } else if constexpr (std::is_same_v<T, WorkflowGroup>) {
                return execute_group_action(concrete_item);
            } else {
                LOG_ERROR("[WorkflowEngine] 未知项目类型");
                return false;
            }
        }, item);
    }

    bool execute_single_action(const WorkflowAction& action) {
        LOG_INFO("[WorkflowEngine] 执行动作: {} (执行器: {})",
                 action.operation, executor_type_to_string(action.executor));

        bool success = false;

        try {
            switch (action.executor) {
                case ExecutorType::MAIN_ROBOTIC_ARM:
                    success = execute_main_robotic_arm_action(action);
                    break;
                case ExecutorType::SUB_ROBOTIC_ARM:
                    success = execute_sub_robotic_arm_action(action);
                    break;
                case ExecutorType::CUP_DISPENSER:
                    success = execute_cup_dispenser_action(action);
                    break;
                case ExecutorType::COFFEE_MACHINE:
                    success = execute_coffee_machine_action(action);
                    break;
                case ExecutorType::MILK_CONTAINER_CLEANER:
                    success = execute_milk_cleaner_action(action);
                    break;
                default:
                    LOG_ERROR("[WorkflowEngine] 未知执行器类型");
                    return false;
            }

            if (success) {
                LOG_INFO("[WorkflowEngine] 动作执行成功: {}", action.operation);
            } else {
                LOG_ERROR("[WorkflowEngine] 动作执行失败: {}", action.operation);
            }

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 动作执行异常: {} - {}", action.operation, e.what());
            success = false;
        }

        return success;
    }

    bool execute_wait_action(const WorkflowWait& wait) {
        LOG_INFO("[WorkflowEngine] 等待 {} 秒", wait.duration);
        std::this_thread::sleep_for(std::chrono::seconds(wait.duration));
        return true;
    }

    bool execute_group_action(const WorkflowGroup& group) {
        LOG_INFO("[WorkflowEngine] 执行组: {} (执行模式: {})",
                 group.name, mode_type_to_string(group.mode));

        if (group.mode == ModeType::SEQUENTIAL) {
            return execute_items_sequential(group.items);
        } else {
            return execute_items_parallel(group.items);
        }
    }

    bool execute_main_robotic_arm_action(const WorkflowAction& action) {
        if (!main_robotic_arm_) {
            LOG_ERROR("[WorkflowEngine] 主机械臂未初始化");
            return false;
        }

        return main_robotic_arm_->execute_action(action.operation, action.parameters.params);
    }

    bool execute_sub_robotic_arm_action(const WorkflowAction& action) {
        if (!sub_robotic_arm_) {
            LOG_ERROR("[WorkflowEngine] 副机械臂未初始化");
            return false;
        }

        return sub_robotic_arm_->execute_action(action.operation, action.parameters.params);
    }

    bool execute_cup_dispenser_action(const WorkflowAction& action) {
        if (!cup_dispenser_) {
            LOG_ERROR("[WorkflowEngine] 杯子分配器未初始化");
            return false;
        }

        return cup_dispenser_->execute_action(action.operation, action.parameters.params);
    }

    bool execute_coffee_machine_action(const WorkflowAction& action) {
        if (!coffee_machine_) {
            LOG_ERROR("[WorkflowEngine] 咖啡机未初始化");
            return false;
        }

        return coffee_machine_->execute_action(action.operation, action.parameters.params);
    }

    bool execute_milk_cleaner_action(const WorkflowAction& action) {
        if (!milk_container_cleaner_) {
            LOG_ERROR("[WorkflowEngine] 牛奶容器清洁器未初始化");
            return false;
        }

        return milk_container_cleaner_->execute_action(action.operation, action.parameters.params);
    }

    std::shared_ptr<DeviceManager> device_manager_;
    std::shared_ptr<MainRoboticArm> main_robotic_arm_;
    std::shared_ptr<SubRoboticArm> sub_robotic_arm_;
    std::shared_ptr<CupDispenser> cup_dispenser_;
    std::shared_ptr<CoffeeMachine> coffee_machine_;
    std::shared_ptr<MilkContainerCleaner> milk_container_cleaner_;

    std::map<std::string, CoffeeWorkflow> workflows_;

    std::atomic<bool> is_executing_;
    std::atomic<bool> stop_requested_;
    std::string current_step_;
};

// CoffeeWorkflowEngine 公共接口实现
CoffeeWorkflowEngine::CoffeeWorkflowEngine(std::shared_ptr<DeviceManager> device_manager) {
    impl_ = std::make_unique<Impl>(device_manager);
}

CoffeeWorkflowEngine::~CoffeeWorkflowEngine() = default;

bool CoffeeWorkflowEngine::load_workflows_from_directory(const std::string& workflow_directory) {
    return impl_->load_workflows_from_directory(workflow_directory);
}

bool CoffeeWorkflowEngine::load_single_workflow(const std::string& workflow_file_path) {
    return impl_->load_single_workflow(workflow_file_path);
}

std::vector<std::string> CoffeeWorkflowEngine::get_available_workflows() const {
    return impl_->get_available_workflows();
}

CoffeeWorkflow CoffeeWorkflowEngine::get_workflow(const std::string& workflow_name) const {
    return impl_->get_workflow(workflow_name);
}

bool CoffeeWorkflowEngine::execute_workflow(const std::string& workflow_name) {
    return impl_->execute_workflow(workflow_name);
}

bool CoffeeWorkflowEngine::execute_workflow(const CoffeeWorkflow& workflow) {
    return impl_->execute_workflow(workflow);
}

bool CoffeeWorkflowEngine::stop_current_workflow() {
    return impl_->stop_current_workflow();
}

bool CoffeeWorkflowEngine::is_executing() const {
    return impl_->is_executing();
}

std::string CoffeeWorkflowEngine::get_current_step() const {
    return impl_->get_current_step();
}

// 执行器类型映射表 - 统一管理字符串和枚举的映射关系
namespace {
    const std::map<std::string, ExecutorType> EXECUTOR_STRING_TO_TYPE = {
        {"main_robotic_arm", ExecutorType::MAIN_ROBOTIC_ARM},
        {"sub_robotic_arm", ExecutorType::SUB_ROBOTIC_ARM},
        {"cup_dispenser", ExecutorType::CUP_DISPENSER},
        {"coffee_machine", ExecutorType::COFFEE_MACHINE},
        {"milk_container_cleaner", ExecutorType::MILK_CONTAINER_CLEANER}
    };

    const std::map<ExecutorType, std::string> EXECUTOR_TYPE_TO_STRING = {
        {ExecutorType::MAIN_ROBOTIC_ARM, "main_robotic_arm"},
        {ExecutorType::SUB_ROBOTIC_ARM, "sub_robotic_arm"},
        {ExecutorType::CUP_DISPENSER, "cup_dispenser"},
        {ExecutorType::COFFEE_MACHINE, "coffee_machine"},
        {ExecutorType::MILK_CONTAINER_CLEANER, "milk_container_cleaner"},
        {ExecutorType::NONE, "none"}
    };

    const std::map<std::string, ModeType> EXECUTION_STRING_TO_TYPE = {
        {"sequential", ModeType::SEQUENTIAL},
        {"parallel", ModeType::PARALLEL}
    };

    const std::map<ModeType, std::string> EXECUTION_TYPE_TO_STRING = {
        {ModeType::SEQUENTIAL, "sequential"},
        {ModeType::PARALLEL, "parallel"}
    };
}

// 辅助函数实现
ModeType string_to_mode_type(const std::string& type_string) {
    auto it = EXECUTION_STRING_TO_TYPE.find(type_string);
    if (it != EXECUTION_STRING_TO_TYPE.end()) {
        return it->second;
    }
    LOG_WARN("[WorkflowEngine] 未知的执行类型: {}, 使用默认值 sequential", type_string);
    return ModeType::SEQUENTIAL;
}

ExecutorType string_to_executor_type(const std::string& executor_string) {
    auto it = EXECUTOR_STRING_TO_TYPE.find(executor_string);
    if (it != EXECUTOR_STRING_TO_TYPE.end()) {
        return it->second;
    }
    LOG_WARN("[WorkflowEngine] 未知的执行器类型: {}, 使用默认值 NONE", executor_string);
    return ExecutorType::NONE;
}

std::string mode_type_to_string(ModeType type) {
    auto it = EXECUTION_TYPE_TO_STRING.find(type);
    if (it != EXECUTION_TYPE_TO_STRING.end()) {
        return it->second;
    }
    return "sequential";
}

std::string executor_type_to_string(ExecutorType executor) {
    auto it = EXECUTOR_TYPE_TO_STRING.find(executor);
    if (it != EXECUTOR_TYPE_TO_STRING.end()) {
        return it->second;
    }
    return "none";
}

} // namespace aubo
