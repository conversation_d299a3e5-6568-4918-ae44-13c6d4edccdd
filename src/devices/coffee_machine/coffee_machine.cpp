/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_machine.h"

#include <chrono>
#include <random>
#include <sstream>
#include <thread>

#include <aubo-base/log.h>

namespace aubo {

class CoffeeMachine::Impl {
public:
    Impl() {
    }

    bool init() {
        LOG_INFO("[CoffeeMachine] 初始化咖啡机");

        // 模拟初始化过程
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));

        LOG_INFO("[CoffeeMachine] 咖啡机初始化完成");
        return true;
    }

    bool shutdown() {
        LOG_INFO("[CoffeeMachine] 关闭咖啡机");
        return true;
    }

    bool emergency_stop() {
        LOG_WARN("[CoffeeMachine] 咖啡机紧急停止");
        return true;
    }

    bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &/*action_params*/) {
        LOG_INFO("[CoffeeMachine] 执行动作: {}", action_name);

        // 模拟动作执行
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        return true;
    }

    std::string get_status() const {
        std::ostringstream oss;
        oss << "咖啡机状态:\n";
        return oss.str();
    }
private:
    int get_brew_time(int volume_ml) const {
        // 根据毫升数计算制作时间，大约每100毫升需要15秒
        return std::max(20, (volume_ml * 15) / 100);
    }
};

// CoffeeMachine 公共接口实现
CoffeeMachine::CoffeeMachine() {
    impl_ = std::make_unique<Impl>();
}

CoffeeMachine::~CoffeeMachine() = default;

bool CoffeeMachine::init() {
    return impl_->init();
}

bool CoffeeMachine::shutdown() {
    return impl_->shutdown();
}

bool CoffeeMachine::emergency_stop() {
    return impl_->emergency_stop();
}

bool CoffeeMachine::execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params) {
    return impl_->execute_action(action_name, action_params);
}

std::string CoffeeMachine::get_status() const {
    return impl_->get_status();
}

} // namespace aubo
