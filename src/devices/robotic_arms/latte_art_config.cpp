/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "latte_art_config.h"

#include <filesystem>
#include <fstream>
#include <map>

#include <aubo-base/log.h>
#include <nlohmann/json.hpp>

namespace aubo {

using json = nlohmann::json;

/**
 * @class LatteArtConfig::Impl
 * @brief LatteArtConfig的内部实现
 */
class LatteArtConfig::Impl {
public:
    Impl() : loaded_(false) {}

    bool load_from_directory(const std::string& config_dir_path) {
        config_dir_path_ = config_dir_path;
        trajectories_.clear();
        loaded_ = false;

        try {
            if (!std::filesystem::exists(config_dir_path)) {
                LOG_WARN("[LatteArtConfig] 配置目录不存在: {}", config_dir_path);
                return false;
            }

            // 遍历目录中的JSON文件
            for (const auto& entry : std::filesystem::directory_iterator(config_dir_path)) {
                if (entry.is_regular_file() && entry.path().extension() == ".json") {
                    std::string art_type = entry.path().stem().string();
                    if (!art_type.empty()) {
                        if (load_trajectory_file(entry.path().string(), art_type)) {
                            LOG_INFO("[LatteArtConfig] 加载拉花配置: {}", art_type);
                        } else {
                            LOG_WARN("[LatteArtConfig] 加载拉花配置失败: {}", art_type);
                        }
                    }
                }
            }

            loaded_ = !trajectories_.empty();
            if (loaded_) {
                LOG_INFO("[LatteArtConfig] 成功加载 {} 个拉花配置", trajectories_.size());
            }

            return loaded_;
        } catch (const std::exception& e) {
            LOG_ERROR("[LatteArtConfig] 加载配置目录失败: {}", e.what());
            return false;
        }
    }

    bool is_loaded() const {
        return loaded_;
    }

    std::vector<std::vector<double>> get_main_robotic_arm_waypoints(const std::string &art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second.main_robotic_arm_waypoints;
        }
        return {};
    }

    std::vector<std::vector<double>> get_sub_robotic_arm_waypoints(const std::string &art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second.sub_robotic_arm_waypoints;
        }
        return {};
    }

    MotionParameters get_motion_parameters(const std::string &art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second.motion_params;
        }
        return MotionParameters(); // 返回默认参数
    }

    LatteArtTrajectory get_trajectory(const std::string &art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second;
        }
        return LatteArtTrajectory();
    }

private:
    bool load_trajectory_file(const std::string& file_path, const std::string &art_type) {
        try {
            std::ifstream file(file_path);
            if (!file.is_open()) {
                LOG_ERROR("[LatteArtConfig] 无法打开文件: {}", file_path);
                return false;
            }

            json j;
            file >> j;

            LatteArtTrajectory trajectory;
            trajectory.name = j.value("name", "");
            trajectory.description = j.value("description", "");

            // 加载运动参数
            if (j.contains("motion_parameters")) {
                auto& mp = j["motion_parameters"];
                trajectory.motion_params.joint_acceleration = mp.value("joint_acceleration", 2.0);
                trajectory.motion_params.joint_velocity = mp.value("joint_velocity", 2.0);
                trajectory.motion_params.blend_radius = mp.value("blend_radius", 0.01);
            }

            // 加载主机械臂路径点
            if (j.contains("main_robotic_arm_waypoints")) {
                trajectory.main_robotic_arm_waypoints = j["main_robotic_arm_waypoints"];
            }

            // 加载副机械臂路径点
            if (j.contains("sub_robotic_arm_waypoints")) {
                trajectory.sub_robotic_arm_waypoints = j["sub_robotic_arm_waypoints"];
            }

            trajectories_[art_type] = trajectory;
            return true;

        } catch (const std::exception& e) {
            LOG_ERROR("[LatteArtConfig] 解析文件失败 {}: {}", file_path, e.what());
            return false;
        }
    }

    bool loaded_;
    std::string config_dir_path_;
    std::map<std::string, LatteArtTrajectory> trajectories_;
};

// LatteArtConfig 公共接口实现
LatteArtConfig::LatteArtConfig() {
    impl_ = std::make_unique<Impl>();
}

LatteArtConfig::~LatteArtConfig() = default;

bool LatteArtConfig::load_from_directory(const std::string& config_dir_path) {
    return impl_->load_from_directory(config_dir_path);
}

bool LatteArtConfig::is_loaded() const {
    return impl_->is_loaded();
}

std::vector<std::vector<double>> LatteArtConfig::get_main_robotic_arm_waypoints(const std::string &art_type) const {
    return impl_->get_main_robotic_arm_waypoints(art_type);
}

std::vector<std::vector<double>> LatteArtConfig::get_sub_robotic_arm_waypoints(const std::string &art_type) const {
    return impl_->get_sub_robotic_arm_waypoints(art_type);
}

MotionParameters LatteArtConfig::get_motion_parameters(const std::string &art_type) const {
    return impl_->get_motion_parameters(art_type);
}

LatteArtTrajectory LatteArtConfig::get_trajectory(const std::string &art_type) const {
    return impl_->get_trajectory(art_type);
}

} // namespace aubo
