/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_SUB_ROBOTIC_ARM_H
#define AUBO_COFFEE_SERVICE_SUB_ROBOTIC_ARM_H

#include <memory>
#include <string>

#include <nlohmann/json.hpp>


namespace aubo {

/**
 * @class SubRoboticArm
 * @brief 副机械臂控制类
 *
 * 提供辅助支持功能，包括接牛奶、接冰块等配料操作。
 */
class SubRoboticArm {
public:
    /**
     * @brief 构造函数
     */
    SubRoboticArm();

    /**
     * @brief 析构函数
     */
    ~SubRoboticArm();

    /**
     * @brief 初始化机械臂
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机械臂连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params = {});

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_SUB_ROBOTIC_ARM_H
