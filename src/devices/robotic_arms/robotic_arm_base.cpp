/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "robotic_arm_base.h"

#include <chrono>
#include <thread>

#include <aubo-base/log.h>

namespace aubo {

// TODO: 使用 <aubo-base/log.h> 报错，临时定义不报错
#define TEMP_LOG_INFO(fmt, ...)
#define TEMP_LOG_ERROR(fmt, ...)
#define TEMP_LOG_WARN(fmt, ...)
#define TEMP_LOG_DEBUG(fmt, ...)

// RoboticArmBase 实现
RoboticArmBase::RoboticArmBase(const std::string& arm_name, const std::string& host, int port)
    : arm_name_(arm_name), host_(host), port_(port), is_connected_(false), is_initialized_(false) {
}

RoboticArmBase::~RoboticArmBase() = default;

bool RoboticArmBase::init() {
    TEMP_LOG_INFO("[{}] 初始化机械臂", arm_name_);

    if (is_initialized_) {
        TEMP_LOG_INFO("[{}] 机械臂已经初始化", arm_name_);
        return true;
    }

    // 1. 连接到机械臂
    if (!connect()) {
        TEMP_LOG_ERROR("[{}] 连接失败，无法初始化", arm_name_);
        return false;
    }

#ifndef NOT_REAL_ROBOT
    // 2. 启动机械臂
    aubo_robot_namespace::ROBOT_SERVICE_STATE state;
    aubo_robot_namespace::ToolDynamicsParam tool_dynamics_param{};

    int result = robot_service_.rootServiceRobotStartup(tool_dynamics_param, 13, true, true, 1000, state);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        TEMP_LOG_ERROR("[{}] 启动失败, 结果 = {}", arm_name_, result);
        disconnect(); // 启动失败时断开连接
        return false;
    }

    // 3. 初始化运动配置
    result = robot_service_.robotServiceInitGlobalMoveProfile();
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        TEMP_LOG_ERROR("[{}] 初始化全局运动配置失败, 结果 = {}", arm_name_, result);
        return false;
    }

    // 4. 配置默认运动参数
    set_default_movement_parameters();

    // 5. 加载拉花配置
    if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
        TEMP_LOG_WARN("[{}] 无法加载拉花配置目录，请检查配置文件");
    } else {
        TEMP_LOG_INFO("[{}] 拉花配置加载成功");
    }
#endif

    is_initialized_ = true;
    // 6. 调用子类特定的初始化步骤
    if (!on_init_specific()) {
        TEMP_LOG_ERROR("[{}] 子类特定初始化失败", arm_name_);
        return false;
    }

    TEMP_LOG_INFO("[{}] 机械臂初始化完成", arm_name_);
    return true;
}

bool RoboticArmBase::shutdown() {
    TEMP_LOG_INFO("[{}] 关闭机械臂", arm_name_);

    bool result = disconnect();
    if (!result) {
        TEMP_LOG_ERROR("[{}] 关闭失败", arm_name_);
    }

    is_initialized_ = false;

    return result;
}

bool RoboticArmBase::emergency_stop() {
    TEMP_LOG_WARN("[{}] 执行紧急停止", arm_name_);

    if (!is_connected_) {
        TEMP_LOG_ERROR("[{}] 机械未连接", arm_name_);
        return false;
    }

    // 简化的紧急停止实现
    TEMP_LOG_INFO("[{}] 紧急停止完成", arm_name_);
    return true;
}

bool RoboticArmBase::move_joint(const std::vector<std::vector<double>>& waypoints) {
    // 参数验证
    if (waypoints.empty()) {
        TEMP_LOG_ERROR("[{}] 无路点数据", arm_name_);
        return false;
    }

    // 状态检查
    if (!check_arm_state()) {
        return false;
    }

#ifdef NOT_REAL_ROBOT
    // 模拟关节运动
    std::this_thread::sleep_for(std::chrono::seconds(2));
    if (waypoints.size() > 1) {
        TEMP_LOG_DEBUG("[{}] 轨迹运动完成", arm_name_);
    } else {
        TEMP_LOG_DEBUG("[{}] 单点运动完成", arm_name_);
    }
    return true;
#endif

    if (waypoints.size() == 1) {
        // 单个路点：直接移动
        TEMP_LOG_INFO("[{}] 移动到单个路点", arm_name_);

        aubo_robot_namespace::wayPoint_S wp;
        for (size_t i = 0; i < waypoints[0].size(); ++i) {
            wp.jointpos[i] = waypoints[0][i];
        }

        int result = robot_service_.robotServiceJointMove(wp, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            TEMP_LOG_ERROR("[{}] 移动到单个路点失败, 错误码: {}", arm_name_, result);
            return false;
        }

        TEMP_LOG_DEBUG("[{}] 单个路点移动完成", arm_name_);
    } else {

        // 添加所有路点
        for (size_t i = 0; i < waypoints.size(); ++i) {
            aubo_robot_namespace::wayPoint_S wp;
            for (size_t j = 0; j < waypoints[i].size(); ++j) {
                wp.jointpos[j] = waypoints[i][j];
            }

            int result = robot_service_.robotServiceJointMove(wp, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                TEMP_LOG_ERROR("[{}] 移动到路点{}失败, 错误码: {}", arm_name_, i, result);
                return false;
            }
        }

        // // 多个路点：轨迹运动
        // TEMP_LOG_INFO("[{}] 沿轨迹移动，路点数量: {}", arm_name_, waypoints.size());

        // // 清空之前的路点
        // robot_service_.robotServiceClearGlobalWayPointVector();

        // aubo_robot_namespace::wayPoint_S current_waypoint;
        // robot_service_.robotServiceGetCurrentWaypointInfo(current_waypoint);
        // robot_service_.robotServiceAddGlobalWayPoint(current_waypoint);

        // int result = robot_service_.robotServiceJointMove(current_waypoint, true);
        // if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        //     TEMP_LOG_ERROR("[{}] 移动到当前路点失败, 错误码: {}", arm_name_, result);
        //     return false;
        // }

        // // 添加所有路点
        // for (size_t i = 0; i < waypoints.size(); ++i) {
        //     aubo_robot_namespace::wayPoint_S wp;
        //     for (size_t j = 0; j < waypoints[i].size(); ++j) {
        //         wp.jointpos[j] = waypoints[i][j];
        //     }

        //     result = robot_service_.robotServiceAddGlobalWayPoint(wp);
        //     if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        //         TEMP_LOG_ERROR("[{}] 添加路点{}失败, 错误码: {}", arm_name_, i, result);
        //         return false;
        //     }
        // }

        // // 执行轨迹运动
        // result = robot_service_.robotServiceTrackMove(aubo_robot_namespace::JOINT_GNUBSPLINEINTP, true);
        // if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        //     TEMP_LOG_ERROR("[{}] 轨迹运动失败, 错误码: {}", arm_name_, result);
        //     return false;
        // }

        TEMP_LOG_DEBUG("[{}] 轨迹运动完成", arm_name_);
    }

    return true;
}

bool RoboticArmBase::connect() {
    TEMP_LOG_INFO("[{}] 连接到机械臂 {}:{}", arm_name_, host_, port_);

#ifdef NOT_REAL_ROBOT
    // 模拟机械臂连接
    std::this_thread::sleep_for(std::chrono::seconds(1));
    is_connected_ = true;
    TEMP_LOG_INFO("[{}] 机械臂连接成功", arm_name_);
    return true;
#endif

    try {
        int result = robot_service_.robotServiceLogin(host_.c_str(), port_, "aubo", "123456");
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            TEMP_LOG_INFO("[{}] 机械臂连接成功", arm_name_);
            is_connected_ = true;
            return true;
        } else {
            TEMP_LOG_ERROR("[{}] 机械臂连接失败，错误码: {}", arm_name_, result);
            is_connected_ = false;
            return false;
        }
    } catch (const std::exception& e) {
        TEMP_LOG_ERROR("[{}] 连接异常: {}", arm_name_, e.what());
        is_connected_ = false;
        return false;
    }
}

bool RoboticArmBase::disconnect() {
    if (!is_connected_) {
        return true;
    }

    TEMP_LOG_INFO("[{}] 断开机械臂连接", arm_name_);

#ifdef NOT_REAL_ROBOT
    // 模拟机械臂断开连接
    std::this_thread::sleep_for(std::chrono::seconds(1));
    is_connected_ = false;
    return true;
#endif

    robot_service_.robotServiceLogout();
    is_connected_ = false;
    return true;
}

bool RoboticArmBase::check_arm_state() {
    if (!is_connected_) {
        TEMP_LOG_ERROR("[{}] 无法操作: 未连接", arm_name_);
        return false;
    }

    if (!is_initialized_) {
        TEMP_LOG_ERROR("[{}] 无法操作: 未初始化", arm_name_);
        return false;
    }

    return true;
}

void RoboticArmBase::set_default_movement_parameters() {
    TEMP_LOG_DEBUG("[{}] 配置默认运动参数", arm_name_);

    // 设置默认的关节运动参数
    aubo_robot_namespace::JointVelcAccParam joint_acc_params;
    for (int i = 0; i < 6; i++) {
        joint_acc_params.jointPara[i] = 2.0;
    }
    // 设置最大加速度
    int result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_acc_params);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        TEMP_LOG_WARN("[{}] 设置关节最大加速度失败, 错误码: {}", arm_name_, result);
    }

    aubo_robot_namespace::JointVelcAccParam joint_velc_params;
    for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
        joint_velc_params.jointPara[i] = 2.0;
    }
    // 设置最大速度
    result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_velc_params);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        TEMP_LOG_WARN("[{}] 设置关节最大速度失败, 错误码: {}", arm_name_, result);
    }

    TEMP_LOG_DEBUG("[{}] 默认运动参数配置完成", arm_name_);
}

bool RoboticArmBase::prepare_latte_art(const std::map<std::string, std::any> &action_params) {
    TEMP_LOG_INFO("[{}] 准备拉花动作", arm_name_);

#ifdef NOT_REAL_ROBOT
    std::this_thread::sleep_for(std::chrono::seconds(1));
    TEMP_LOG_INFO("[{}] 准备拉花动作完成", arm_name_);
    return true;
#endif

    if (action_params.find("art_type") == action_params.end() ||
        !action_params.at("art_type").has_value()) {
        TEMP_LOG_ERROR("[{}] 拉花动作类型未指定", arm_name_);
        return false;
    }

    std::string art_type = std::any_cast<std::string>(action_params.at("art_type"));

    std::vector<std::vector<double>> waypoints;
    if (arm_name_ == "MainRoboticArm") {
        waypoints = latte_art_config_.get_main_robotic_arm_waypoints(art_type);
    } else if (arm_name_ == "SubRoboticArm") {
        waypoints = latte_art_config_.get_sub_robotic_arm_waypoints(art_type);
    } else {
        TEMP_LOG_ERROR("[{}] 未知的机械臂名称: {}", arm_name_, arm_name_);
        return false;
    }
    if (waypoints.empty()) {
        TEMP_LOG_ERROR("[{}] 拉花动作类型{}不存在", arm_name_, art_type);
        return false;
    }

    move_joint({waypoints[0]});

    robot_service_.robotServiceClearGlobalWayPointVector();
    for (auto &waypoint : waypoints) {
        aubo_robot_namespace::wayPoint_S wp;
        for (size_t i = 0; i < waypoint.size(); ++i) {
            wp.jointpos[i] = waypoint[i];
        }

        int result = robot_service_.robotServiceAddGlobalWayPoint(wp);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            TEMP_LOG_ERROR("[{}] 添加路点失败, 错误码: {}", arm_name_, result);
            return false;
        }
    }

    TEMP_LOG_INFO("[{}] 准备拉花动作完成", arm_name_);
    return true;
}

bool RoboticArmBase::do_latte_art(const std::map<std::string, std::any> &action_params) {
    TEMP_LOG_INFO("[{}] 执行拉花动作", arm_name_);

#ifdef NOT_REAL_ROBOT
    std::this_thread::sleep_for(std::chrono::seconds(1));
    TEMP_LOG_INFO("[{}] 执行拉花动作完成", arm_name_);
    return true;
#endif

    if (!check_arm_state()) {
        return false;
    }

    if (action_params.find("art_type") == action_params.end() ||
        !action_params.at("art_type").has_value()) {
        TEMP_LOG_ERROR("[{}] 拉花动作类型未指定", arm_name_);
        return false;
    }

    std::string art_type = std::any_cast<std::string>(action_params.at("art_type"));

    auto blend_radius = robot_service_.robotServiceGetGlobalBlendRadius();
    aubo_robot_namespace::JointVelcAccParam previous_joint_acc_params;
    robot_service_.robotServiceGetGlobalMoveJointMaxAcc(previous_joint_acc_params);
    aubo_robot_namespace::JointVelcAccParam previous_joint_velc_params;
    robot_service_.robotServiceGetGlobalMoveJointMaxVelc(previous_joint_velc_params);

    {
        auto motion_params = latte_art_config_.get_motion_parameters(art_type);
        robot_service_.robotServiceSetGlobalBlendRadius(motion_params.blend_radius);
        aubo_robot_namespace::JointVelcAccParam joint_velc_param;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_velc_param.jointPara[i] = motion_params.joint_velocity;
        }
        robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_velc_param);

        aubo_robot_namespace::JointVelcAccParam joint_acc_param;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_acc_param.jointPara[i] = motion_params.joint_acceleration;
        }
        robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_acc_param);
    }

    int result = robot_service_.robotServiceTrackMove(aubo_robot_namespace::JOINT_GNUBSPLINEINTP, true);

    // 恢复之前的运动参数
    {
        robot_service_.robotServiceSetGlobalBlendRadius(blend_radius);
        robot_service_.robotServiceSetGlobalMoveJointMaxAcc(previous_joint_acc_params);
        robot_service_.robotServiceSetGlobalMoveJointMaxVelc(previous_joint_velc_params);
    }

    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        TEMP_LOG_ERROR("[{}] 执行拉花动作失败, 错误码: {}", arm_name_, result);
        return false;
    }

    TEMP_LOG_INFO("[{}] 执行拉花动作完成", arm_name_);
    return true;
}

} // namespace aubo
