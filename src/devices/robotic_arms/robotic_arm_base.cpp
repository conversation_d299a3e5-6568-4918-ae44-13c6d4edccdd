/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "robotic_arm_base.h"

#include <chrono>
#include <thread>

// #include <aubo-base/log.h>

namespace aubo {

// TODO: 使用 <aubo-base/log.h> 报错，临时定义不报错
#define LOG_INFO(fmt, ...)
#define LOG_ERROR(fmt, ...)
#define LOG_WARN(fmt, ...)
#define LOG_DEBUG(fmt, ...)

#define NOT_REAL_ROBOT

// RoboticArmBase 实现
RoboticArmBase::RoboticArmBase(const std::string& arm_name, const std::string& host, int port)
    : arm_name_(arm_name), host_(host), port_(port), is_connected_(false), is_initialized_(false) {
}

RoboticArmBase::~RoboticArmBase() = default;

bool RoboticArmBase::init() {
    LOG_INFO("[{}] 初始化机械臂", arm_name_);

    if (is_initialized_) {
        LOG_INFO("[{}] 机械臂已经初始化", arm_name_);
        return true;
    }

    // 1. 连接到机械臂
    if (!connect()) {
        LOG_ERROR("[{}] 连接失败，无法初始化", arm_name_);
        return false;
    }

#ifndef NOT_REAL_ROBOT
    // 2. 启动机械臂
    aubo_robot_namespace::ROBOT_SERVICE_STATE state;
    aubo_robot_namespace::ToolDynamicsParam tool_dynamics_param{};

    int result = robot_service_.rootServiceRobotStartup(tool_dynamics_param, 13, true, true, 1000, state);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_ERROR("[{}] 启动失败, 结果 = {}", arm_name_, result);
        disconnect(); // 启动失败时断开连接
        return false;
    }

    // 3. 初始化运动配置
    result = robot_service_.robotServiceInitGlobalMoveProfile();
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_ERROR("[{}] 初始化全局运动配置失败, 结果 = {}", arm_name_, result);
        return false;
    }

    // 4. 配置默认运动参数
    set_default_movement_parameters();
#endif

    // 5. 调用子类特定的初始化步骤
    if (!on_init_specific()) {
        LOG_ERROR("[{}] 子类特定初始化失败", arm_name_);
        return false;
    }

    is_initialized_ = true;

    LOG_INFO("[{}] 机械臂初始化完成", arm_name_);
    return true;
}

bool RoboticArmBase::shutdown() {
    LOG_INFO("[{}] 关闭机械臂", arm_name_);

    bool result = disconnect();
    if (!result) {
        LOG_ERROR("[{}] 关闭失败", arm_name_);
    }

    is_initialized_ = false;

    return result;
}

bool RoboticArmBase::emergency_stop() {
    LOG_WARN("[{}] 执行紧急停止", arm_name_);

    if (!is_connected_) {
        LOG_ERROR("[{}] 机械未连接", arm_name_);
        return false;
    }

    // 简化的紧急停止实现
    LOG_INFO("[{}] 紧急停止完成", arm_name_);
    return true;
}

bool RoboticArmBase::move_joint(const std::vector<std::vector<double>>& waypoints) {
    // 参数验证
    if (waypoints.empty()) {
        LOG_ERROR("[{}] 无路点数据", arm_name_);
        return false;
    }

    // 状态检查
    if (!check_arm_state()) {
        return false;
    }

#ifdef NOT_REAL_ROBOT
    // 模拟关节运动
    std::this_thread::sleep_for(std::chrono::seconds(2));
    if (waypoints.size() > 1) {
        LOG_DEBUG("[{}] 轨迹运动完成", arm_name_);
    } else {
        LOG_DEBUG("[{}] 单点运动完成", arm_name_);
    }
    return true;
#endif

    // 验证路点数据有效性
    for (size_t i = 0; i < waypoints.size(); ++i) {
        if (waypoints[i].size() != 6) {
            LOG_ERROR("[{}] 路点{}关节数量错误: 期望6个，实际{}个", arm_name_, i, waypoints[i].size());
            return false;
        }
    }

    if (waypoints.size() == 1) {
        // 单个路点：直接移动
        LOG_INFO("[{}] 移动到单个路点", arm_name_);

        aubo_robot_namespace::wayPoint_S wp;
        for (size_t i = 0; i < waypoints[0].size(); ++i) {
            wp.jointpos[i] = waypoints[0][i];
        }

        int result = robot_service_.robotServiceJointMove(wp, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[{}] 移动到单个路点失败, 错误码: {}", arm_name_, result);
            return false;
        }

        LOG_DEBUG("[{}] 单个路点移动完成", arm_name_);
    } else {
        // 多个路点：轨迹运动
        LOG_INFO("[{}] 沿轨迹移动，路点数量: {}", arm_name_, waypoints.size());

        // 清空之前的路点
        robot_service_.robotServiceClearGlobalWayPointVector();

        // 添加所有路点
        for (size_t i = 0; i < waypoints.size(); ++i) {
            aubo_robot_namespace::wayPoint_S wp;
            for (size_t j = 0; j < waypoints[i].size(); ++j) {
                wp.jointpos[j] = waypoints[i][j];
            }

            int result = robot_service_.robotServiceAddGlobalWayPoint(wp);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[{}] 添加路点{}失败, 错误码: {}", arm_name_, i, result);
                return false;
            }
        }

        // 执行轨迹运动
        int result = robot_service_.robotServiceTrackMove(aubo_robot_namespace::JOINT_GNUBSPLINEINTP, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[{}] 轨迹运动失败, 错误码: {}", arm_name_, result);
            return false;
        }

        LOG_DEBUG("[{}] 轨迹运动完成", arm_name_);
    }

    return true;
}

bool RoboticArmBase::connect() {
    LOG_INFO("[{}] 连接到机械臂 {}:{}", arm_name_, host_, port_);

#ifdef NOT_REAL_ROBOT
    // 模拟机械臂连接
    std::this_thread::sleep_for(std::chrono::seconds(1));
    is_connected_ = true;
    LOG_INFO("[{}] 机械臂连接成功", arm_name_);
    return true;
#endif

    try {
        int result = robot_service_.robotServiceLogin(host_.c_str(), port_, "aubo", "123456");
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_INFO("[{}] 机械臂连接成功", arm_name_);
            is_connected_ = true;
            return true;
        } else {
            LOG_ERROR("[{}] 机械臂连接失败，错误码: {}", arm_name_, result);
            is_connected_ = false;
            return false;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("[{}] 连接异常: {}", arm_name_, e.what());
        is_connected_ = false;
        return false;
    }
}

bool RoboticArmBase::disconnect() {
    if (!is_connected_) {
        return true;
    }

    LOG_INFO("[{}] 断开机械臂连接", arm_name_);

#ifdef NOT_REAL_ROBOT
    // 模拟机械臂断开连接
    std::this_thread::sleep_for(std::chrono::seconds(1));
    is_connected_ = false;
    return true;
#endif

    robot_service_.robotServiceLogout();
    is_connected_ = false;
    return true;
}

bool RoboticArmBase::check_arm_state() {
    if (!is_connected_) {
        LOG_ERROR("[{}] 无法操作: 未连接", arm_name_);
        return false;
    }

    if (!is_initialized_) {
        LOG_ERROR("[{}] 无法操作: 未初始化", arm_name_);
        return false;
    }

    return true;
}

void RoboticArmBase::set_default_movement_parameters() {
    LOG_DEBUG("[{}] 配置默认运动参数", arm_name_);

    // 设置默认的关节运动参数
    aubo_robot_namespace::JointVelcAccParam joint_params;
    for (int i = 0; i < 6; i++) {
        joint_params.jointPara[i] = 2.0;
    }

    // 设置最大加速度
    int result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_params);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_WARN("[{}] 设置关节最大加速度失败, 错误码: {}", arm_name_, result);
    }

    // 设置最大速度
    result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_params);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_WARN("[{}] 设置关节最大速度失败, 错误码: {}", arm_name_, result);
    }

    LOG_DEBUG("[{}] 默认运动参数配置完成", arm_name_);
}

} // namespace aubo
