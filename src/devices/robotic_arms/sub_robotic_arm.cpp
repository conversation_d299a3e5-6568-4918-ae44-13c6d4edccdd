/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "sub_robotic_arm.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "latte_art_config.h"
#include "robotic_arm_base.h"

namespace aubo {

// 配置常量
constexpr const char* sub_robotic_arm_HOST = "*************";
constexpr int sub_robotic_arm_PORT = 8899;

class SubRoboticArm::Impl : public RoboticArmBase {
public:
    Impl() : RoboticArmBase("SubRoboticArm", sub_robotic_arm_HOST, sub_robotic_arm_PORT) {}

    ~Impl() {
        LOG_INFO("[SubRoboticArm] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[SubRoboticArm] 执行副机械臂特定初始化");

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[SubRoboticArm] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[SubRoboticArm] 拉花配置加载成功");
        }

        LOG_INFO("[SubRoboticArm] 副机械臂特定初始化完成");
        return true;
    }

    bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params) override {
        LOG_INFO("[SubRoboticArm] 执行动作: {}", action_name);
        if (action_name == "move_to_home") {
            return move_to_home();
        } else if (action_name == "move_to_cleaning_ready") {
            return move_to_cleaning_ready();
        } else if (action_name == "move_to_cleaning") {
            return move_to_cleaning();
        } else if (action_name == "leave_home") {
            return leave_home();
        } else if (action_name == "move_to_ready") {
            return move_to_ready();
        } else if (action_name == "move_to_milk_outlet") {
            return move_to_milk_outlet();
        } else if (action_name == "pick_up_pose_milk") {
            return pick_up_pose_milk();
        } else if (action_name == "complete_pick_up_milk") {
            return complete_pick_up_milk();
        } else if (action_name == "shake_milk") {
            return shake_milk(action_params);
        } else if (action_name == "pour_out_milk") {
            return pour_out_milk();
        } else if (action_name == "prepare_latte_art") {
            return prepare_latte_art(action_params);
        } else if (action_name == "do_latte_art") {
            return do_latte_art(action_params);
        }
        LOG_ERROR("[SubRoboticArm] 未知动作: {}", action_name);
        return false;
    }

private:
    // 移动到HOME位置(只能从清洗位置/倾倒剩余奶泡位置移动过去，其他位置移动过去存在碰撞风险！！！)
    bool move_to_home() {
        LOG_INFO("[SubRoboticArm] 移动到HOME位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 移动到HOME位置完成");
        return true;
#endif

        std::vector<std::vector<double>> home_joint_angle = {
            {0.299558, -0.253792, 2.193381, -0.681089, -1.286692, -3.001649}  // 【HOME位置】
        };

        if (!move_joint(home_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 移动到HOME位置失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 移动到HOME位置完成");
        return true;
    }

    // 移动到准备清洗位置
    bool move_to_cleaning_ready() {
        LOG_INFO("[SubRoboticArm] 移动到准备清洗位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 移动到准备清洗位置完成");
        return true;
#endif

        std::vector<std::vector<double>> cleaning_ready_joint_angle = {
            {0.053472, 0.029112, 1.868314, -1.348693, -1.339303, -1.786542}, //【准备清洗位置1】
            {0.336054, -0.045866, 2.000638, -1.131380, -1.200204, -2.864809}, //【准备清洗位置2】
        };

        if (!move_joint(cleaning_ready_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 移动到准备清洗位置失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 移动到准备清洗位置完成");
        return true;
    }

    // 移动到清洗位置(一般从HOME位置移动过去，其他位置移动过去存在碰撞风险！！！需要先移动到准备清洗位置)
    bool move_to_cleaning() {
        LOG_INFO("[SubRoboticArm] 移动到清洗位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 移动到清洗位置完成");
        return true;
#endif

        std::vector<std::vector<double>> cleaning_joint_angle = {
            {0.284735, -0.381071, 2.162107, -0.644146, -1.228713, -2.868520} // 【清洗位置】
        };

        if (!move_joint(cleaning_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 移动到清洗位置失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 移动到清洗位置完成");
        return true;
    }

    // 离开HOME位置，前往安全位置
    bool leave_home() {
        LOG_INFO("[SubRoboticArm] 离开HOME位置，前往安全位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 离开HOME位置，前往安全位置完成");
        return true;
#endif

        std::vector<std::vector<double>> leave_home_joint_angle = {
            {0.302517, 0.053719, 1.899584, -1.264403, -1.281932, -2.948872},  // 【过渡位置】
            {0.101288, 0.171540, 1.921859, -1.414690, -1.288168, -1.353138}   // 【安全位置】
        };

        if (!move_joint(leave_home_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 离开HOME位置，前往安全位置失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 离开HOME位置，前往安全位置完成");
        return true;
    }

    // 移动到准备位置
    bool move_to_ready() {
        LOG_INFO("[SubRoboticArm] 移动到准备位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 移动到准备位置完成");
        return true;
#endif

        std::vector<std::vector<double>> ready_joint_angle = {
            {-0.054005, 0.256593, 2.192446, -1.230923, -1.207646, 0.114618},  // 【工作准备位置】
        };

        if (!move_joint(ready_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 移动到准备位置失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 移动到准备位置完成");
        return true;
    }

    bool move_to_milk_outlet() {
        LOG_INFO("[SubRoboticArm] 移动到牛奶出口位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 移动到牛奶出口位置完成");
        return true;
#endif

        std::vector<std::vector<double>> milk_outlet_joint_angle = {
            {-0.223398, 0.022299, 1.981184, -1.205320, -0.358279, 0.114224},  // 【牛奶出口】
        };

        if (!move_joint(milk_outlet_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 移动到牛奶出口位置失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 移动到牛奶出口位置完成");
        return true;
    }

    // 取牛奶，注意要先移动到牛奶出口位置
    bool pick_up_pose_milk() {
        LOG_INFO("[SubRoboticArm] 取牛奶姿态");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 取牛奶姿态完成");
        return true;
#endif

        std::vector<std::vector<double>> pick_up_milk_joint_angle = {
            {-0.126652, -0.061707, 1.528101, -1.822993, 0.093292, -0.225399},   // 【倾斜取奶】
        };

        if (!move_joint(pick_up_milk_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 抓取牛奶失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 取牛奶姿态完成");
        return true;
    }

    // 完成取牛奶
    bool complete_pick_up_milk() {
        LOG_INFO("[SubRoboticArm] 完成取牛奶");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 完成取牛奶");
        return true;
#endif

        std::vector<std::vector<double>> complete_pick_up_milk_joint_angle = {
            {-0.223398, 0.022299, 1.981184, -1.205320, -0.358279, 0.114224},    // 【取完奶后回正】
        };

        if (!move_joint(complete_pick_up_milk_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 完成取牛奶失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 完成取牛奶");
        return true;
    }

    // 摇奶
    bool shake_milk(const std::map<std::string, std::any> &action_params) {
        LOG_INFO("[SubRoboticArm] 摇奶");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 摇奶完成");
        return true;
#endif

        int shake_rounds = 15;
        try {
            // 摇奶次数
            if (action_params.find("shake_rounds") == action_params.end() ||
                !action_params.at("shake_rounds").has_value() ||
                std::any_cast<int>(action_params.at("shake_rounds")) <= 0) {
                LOG_WARN("[SubRoboticArm] 摇奶次数未指定，使用默认值15");
            } else {
                shake_rounds = std::any_cast<int>(action_params.at("shake_rounds"));
            }
        } catch(const std::bad_any_cast &e) {
            LOG_ERROR("[SubRoboticArm] 摇奶次数类型错误: {}", e.what());
            return false;
        }

        return true;

        std::vector<std::vector<double>> shake_milk_joint_angle = {
            {-0.054118, 0.259726, 2.186295, -1.240109, -1.207265, 0.114604}, // 【摇奶位置1】
            {-0.151755, 0.267723, 2.191979, -1.241637, -1.304873, 0.112070}, // 【摇奶位置2】
            {-0.218171, 0.161130, 2.110561, -1.216082, -1.371269, 0.110424}  // 【摇奶位置3】
        };

        // 移动到第一个点
        if (!move_joint({shake_milk_joint_angle[0]})) {
            LOG_ERROR("[SubRoboticArm] 移动到第一个摇奶路点失败");
            return false;
        }

        // 清空之前的路点
        robot_service_.robotServiceClearGlobalWayPointVector();
        // 添加所有摇奶路点
        for (size_t i = 0; i < shake_milk_joint_angle.size(); i++) {
            const auto& waypoint = shake_milk_joint_angle[i];
            aubo_robot_namespace::wayPoint_S way_point;
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                way_point.jointpos[j] = waypoint[j];
            }

            int result = robot_service_.robotServiceAddGlobalWayPoint(way_point);
            if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
                // LOG_INFO("[MainRoboticArm] 添加摇奶路点成功: 路点{}", i+1);
            } else {
                LOG_ERROR("[MainRoboticArm] 添加摇奶路点失败: 路点{}, 结果 = {}", i+1, result);
                return false;
            }
        }

        // 设置交融半径
        auto blend_radius = robot_service_.robotServiceGetGlobalBlendRadius();
        robot_service_.robotServiceSetGlobalBlendRadius(0.02);
        robot_service_.robotServiceSetGlobalCircularLoopTimes(shake_rounds);

        // 执行轨迹运动
        int result = robot_service_.robotServiceTrackMove(aubo_robot_namespace::ARC_CIR, true);
        // 恢复交融半径
        robot_service_.robotServiceSetGlobalBlendRadius(blend_radius);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[MainRoboticArm] 摇奶运动失败, 结果 = {}", result);
            return false;
        }

        LOG_INFO("[SubRoboticArm] 摇奶完成");
        return true;
    }

    // 移动到拉花位置
    bool move_to_latte_art() {
        LOG_INFO("[SubRoboticArm] 移动到拉花位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 移动到拉花位置完成");
        return true;
#endif

        std::vector<std::vector<double>> latte_art_joint_angle = {
            {-0.377120, 0.213000, 1.635890, -1.683930, -1.814830, -0.783520},  // 【拉花位置】
        };

        if (!move_joint(latte_art_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 移动到拉花位置失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 移动到拉花位置完成");
        return true;
    }

    // 倾倒剩余奶泡
    bool pour_out_milk() {
        LOG_INFO("[SubRoboticArm] 倾倒剩余奶泡");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[SubRoboticArm] 倾倒剩余奶泡完成");
        return true;
#endif

        std::vector<std::vector<double>> pour_out_leftover_foam_joint_angle = {
            {-0.231389, 0.325317, 1.693122, -1.655029, -1.500510, -0.401216}, // 【倾倒位置1】
            {-0.173430, 0.147302, 2.101791, -1.235604, -1.260543, -0.424707}, // 【倾倒位置2】
            {-0.011403, 0.064681, 1.964967, -1.347639, -1.257074, -1.061824}  // 【倾倒位置3】
        };

        if (!move_joint(pour_out_leftover_foam_joint_angle)) {
            LOG_ERROR("[SubRoboticArm] 倾倒剩余奶泡失败");
            return false;
        }

        LOG_INFO("[SubRoboticArm] 倾倒剩余奶泡完成");
        return true;
    }

private:
    LatteArtConfig latte_art_config_;
};

SubRoboticArm::SubRoboticArm() {
    impl_ = std::make_unique<Impl>();
}

SubRoboticArm::~SubRoboticArm() = default;

bool SubRoboticArm::init() {
    return impl_->init();
}

bool SubRoboticArm::shutdown() {
    return impl_->shutdown();
}

bool SubRoboticArm::emergency_stop() {
    return impl_->emergency_stop();
}

bool SubRoboticArm::execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params) {
    return impl_->execute_action(action_name, action_params);
}

} // namespace aubo
