/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_ROBOTIC_ARM_BASE_H
#define AUBO_COFFEE_SERVICE_ROBOTIC_ARM_BASE_H

#include <any>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "latte_art_config.h"

namespace aubo {

// #define NOT_REAL_ROBOT

/**
 * @class RoboticArmBase
 * @brief 机械臂基类
 *
 * 为所有机械臂提供通用功能实现，包括连接管理、初始化、运动控制等基础功能。
 */
class RoboticArmBase {
public:
    /**
     * @brief 构造函数
     * @param arm_name 机械臂名称
     * @param host 机械臂IP地址
     * @param port 机械臂端口
     */
    RoboticArmBase(const std::string& arm_name, const std::string& host, int port);

    /**
     * @brief 虚析构函数
     */
    virtual ~RoboticArmBase();

    /**
     * @brief 获取机械臂名称
     * @return 机械臂名称
     */
    const std::string& get_name() const { return arm_name_; }

    /**
     * @brief 初始化机械臂（包含连接、启动、配置等完整流程）
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机械臂连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    virtual bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params) = 0;

    /**
     * @brief 关节运动
     * @param waypoints 关节角度序列
     * @return 运动成功返回true
     */
    bool move_joint(const std::vector<std::vector<double>>& waypoints);

protected:
    /**
     * @brief 连接到机械臂
     * @return 连接成功返回true
     */
    bool connect();

    /**
     * @brief 断开机械臂连接
     * @return 断开成功返回true
     */
    bool disconnect();

    /**
     * @brief 检查机械臂状态
     * @return 状态正常返回true
     */
    bool check_arm_state();

    /**
     * @brief 子类特定的初始化步骤（子类可重写）
     * @return 初始化成功返回true
     */
    virtual bool on_init_specific() { return true; }

    /**
     * @brief 设置默认的运动参数
     */
    void set_default_movement_parameters();

    /**
     * @brief 准备拉花动作
     * @param action_params 动作参数
     * @return 准备成功返回true
     */
    bool prepare_latte_art(const std::map<std::string, std::any> &action_params);

    /**
     * @brief 执行拉花动作
     * @return 执行成功返回true
     */
    bool do_latte_art(const std::map<std::string, std::any> &action_params);

protected:
    std::string arm_name_;          ///< 机械臂名称
    std::string host_;              ///< 机械臂IP地址
    int port_;                      ///< 机械臂端口
    bool is_connected_;             ///< 连接状态
    bool is_initialized_;           ///< 初始化状态

    // 机械臂服务接口
    ServiceInterface robot_service_;

    LatteArtConfig latte_art_config_;   /// 拉花配置
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_ROBOTIC_ARM_BASE_H
