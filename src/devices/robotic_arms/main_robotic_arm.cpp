/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "main_robotic_arm.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "latte_art_config.h"
#include "robotic_arm_base.h"

namespace aubo {

// 配置常量
constexpr const char* main_robotic_arm_HOST = "*************";
constexpr int main_robotic_arm_PORT = 8899;

class MainRoboticArm::Impl : public RoboticArmBase {
public:
    Impl() : RoboticArmBase("MainRoboticArm", main_robotic_arm_HOST, main_robotic_arm_PORT) {}

    ~Impl() {
        LOG_INFO("[MainRoboticArm] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[MainRoboticArm] 执行主机械臂特定初始化");


        LOG_INFO("[MainRoboticArm] 主机械臂特定初始化完成");
        return true;
    }

    bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params) override {
        LOG_INFO("[MainRoboticArm] 执行动作: {}", action_name);
        if (action_name == "move_to_home") {
            return move_to_home();
        } else if (action_name == "move_to_ready") {
            return move_to_ready();
        } else if (action_name == "move_to_cup_outlet") {
            return move_to_cup_outlet();
        } else if (action_name == "leave_cup_outlet") {
            return leave_cup_outlet();
        } else if (action_name == "move_to_coffee_outlet") {
            return move_to_coffee_outlet();
        } else if (action_name == "pick_up_pose_coffee") {
            return pick_up_pose_coffee();
        } else if (action_name == "complete_pick_up_coffee") {
            return complete_pick_up_coffee();
        } else if (action_name == "move_to_latte_art") {
            return move_to_latte_art();
        } else if (action_name == "deliver_coffee") {
            return deliver_coffee();
        } else if (action_name == "prepare_latte_art") {
            return prepare_latte_art(action_params);
        } else if (action_name == "do_latte_art") {
            return do_latte_art(action_params);
        }
        LOG_ERROR("[MainRoboticArm] 未知动作: {}", action_name);
        return false;
    }

private:
    bool move_to_home() {
        LOG_INFO("[MainRoboticArm] 移动到home位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[MainRoboticArm] 移动到home位置完成");
        return true;
#endif

        std::vector<std::vector<double>> home_joint_angle = {
            {0.106664, -0.083567, -1.929689, 1.285737, 1.715605, -0.006741} // 【home位置】
        };

        if (move_joint(home_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 移动到home位置失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 移动到home位置完成");
        return true;
    }

    bool move_to_ready() {
        LOG_INFO("[MainRoboticArm] 移动到ready位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[MainRoboticArm] 移动到ready位置完成");
        return true;
#endif

        std::vector<std::vector<double>> ready_joint_angle = {
            {0.394969, -0.582201, -2.294774, 1.378310, 1.567179, -0.005875} // 【准备工作位置】
        };

        if (move_joint(ready_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 移动到ready位置失败");
            return false;
        }
        LOG_INFO("[MainRoboticArm] 移动到ready位置完成");
        return true;
    }

    // 移动到杯子出口位置
    bool move_to_cup_outlet() {
        LOG_INFO("[MainRoboticArm] 移动到杯子出口位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(3));
        LOG_INFO("[MainRoboticArm] 移动到杯子出口位置完成");
        return true;
#endif

        std::vector<std::vector<double>> cup_outlet_joint_angle = {
            {1.748653, -0.582250, -2.294792, 1.377843, 1.567181, -0.005866}, // 【落杯器前方】
            {1.798266, 0.316536, -1.375015, 1.427817, 1.768882, 0.003928},   // 【落杯器中间】
            {1.789321, 0.360326, -1.165683, 1.593793, 1.754975, 0.004124},   // 【落杯器中上抬接杯子】
        };

        if (move_joint(cup_outlet_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 移动到杯子出口位置失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 移动到杯子出口位置完成");
        return true;
    }

    // 离开杯子出口位置
    bool leave_cup_outlet() {
        LOG_INFO("[MainRoboticArm] 离开杯子出口位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[MainRoboticArm] 离开杯子出口位置完成");
        return true;
#endif

        std::vector<std::vector<double>> leave_cup_outlet_joint_angle = {
            {1.798266, 0.316536, -1.375015, 1.427817, 1.768882, 0.003928},   // 【落杯器中间】
            {1.748653, -0.582250, -2.294792, 1.377843, 1.567181, -0.005866}, // 【落杯器前方】
        };

        if (move_joint(leave_cup_outlet_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 离开杯子出口位置失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 离开杯子出口位置完成");
        return true;
    }

    bool move_to_coffee_outlet() {
        LOG_INFO("[MainRoboticArm] 移动到咖啡出口位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[MainRoboticArm] 移动到咖啡出口位置完成");
        return true;
#endif

        std::vector<std::vector<double>> coffee_outlet_joint_angle = {
            {0.455068, 0.281083, -1.454357, 1.425689, 1.488030, -0.034480},  // 【咖啡机出口下方】
        };

        if (move_joint(coffee_outlet_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 移动到咖啡出口位置失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 移动到咖啡出口位置完成");
        return true;
    }

    // 接咖啡姿态
    bool pick_up_pose_coffee() {
        LOG_INFO("[MainRoboticArm] 接咖啡姿态");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(1));
        LOG_INFO("[MainRoboticArm] 接咖啡姿态完成");
        return true;
#endif

        std::vector<std::vector<double>> pick_up_coffee_joint_angle = {
            {0.515712, 0.266762, -1.354910, 1.491814, 1.656176, -0.577095},  // 【杯子倾斜】
        };

        if (move_joint(pick_up_coffee_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 接咖啡姿态失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 接咖啡姿态完成");
        return true;
    }

    // 取咖啡完成
    bool complete_pick_up_coffee() {
        LOG_INFO("[MainRoboticArm] 取咖啡完成姿态");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(1));
        LOG_INFO("[MainRoboticArm] 取咖啡完成姿态完成");
        return true;
#endif

        std::vector<std::vector<double>> leave_coffee_outlet_joint_angle = {
            {0.424998, 0.255695, -1.439219, 1.466285, 1.402348, -0.005819},  // 【咖啡机出口下方水平】
        };

        if (move_joint(leave_coffee_outlet_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 离开咖啡出口位置失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 离开咖啡出口位置完成");
        return true;
    }

    bool move_to_latte_art() {
        LOG_INFO("[MainRoboticArm] 移动到拉花位置");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[MainRoboticArm] 移动到拉花位置完成");
        return true;
#endif

        std::vector<std::vector<double>> latte_art_joint_angle = {
            {-0.057637, -0.517702, -2.347097, 1.260445, 2.236833, -0.005356}, // 【过渡位置1】
            {-0.348188, 0.061563, -1.862573, 1.193162, 1.617185, -0.005369},  // 【过渡位置2】
            {-0.166120, 0.272210, -1.263570, 1.839400, 1.338660, -0.728690}   // 【倾斜】

        };

        if (move_joint(latte_art_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 移动到拉花位置失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 移动到拉花位置完成");
        return true;
    }

    bool deliver_coffee() {
        LOG_INFO("[MainRoboticArm] 交付咖啡");

#ifdef NOT_REAL_ROBOT
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[MainRoboticArm] 交付咖啡完成");
        return true;
#endif

        std::vector<std::vector<double>> deliver_coffee_joint_angle = {
            {-0.348188, 0.061563, -1.862573, 1.193162, 1.617185, -0.005369}, // 【交付准备位置】
            {-0.598221, 0.125717, -2.140542, 0.871622, 1.621427, 0.016601},  // 【交付路径1】
            {-0.598236, 0.214382, -2.183287, 0.740350, 1.621435, 0.016628},  // 【交付路径2】
            {-0.598151, 0.296397, -2.207923, 0.773433, 1.618430, 0.016553},  // 【交付路径3】
            {-0.603985, 0.081795, -2.607261, 0.437128, 1.591528, -0.004395}, // 【交付位置】
            {-0.603985, 0.081795, -2.607261, 0.437128, 1.591528, -0.004395}, // 【交付确认位置】
            {-0.323560, -0.416307, -2.326127, 1.229185, 2.080945, -0.005333} // 【交付完成位置】
        };

        if (move_joint(deliver_coffee_joint_angle)) {
            LOG_ERROR("[MainRoboticArm] 交付咖啡失败");
            return false;
        }

        LOG_INFO("[MainRoboticArm] 交付咖啡完成");
        return true;
    }
};


MainRoboticArm::MainRoboticArm() {
    impl_ = std::make_unique<Impl>();
}

MainRoboticArm::~MainRoboticArm() = default;

bool MainRoboticArm::init() {
    return impl_->init();
}

bool MainRoboticArm::shutdown() {
    return impl_->shutdown();
}

bool MainRoboticArm::emergency_stop() {
    return impl_->emergency_stop();
}

bool MainRoboticArm::execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params) {
    return impl_->execute_action(action_name, action_params);
}

} // namespace aubo
