/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "main_robotic_arm.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "latte_art_config.h"
#include "robotic_arm_base.h"

namespace aubo {

// 配置常量
constexpr const char* main_robotic_arm_HOST = "*************";
constexpr int main_robotic_arm_PORT = 8899;

class MainRoboticArm::Impl : public RoboticArmBase {
public:
    Impl() : RoboticArmBase("MainRoboticArm", main_robotic_arm_HOST, main_robotic_arm_PORT) {}

    ~Impl() {
        LOG_INFO("[MainRoboticArm] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[MainRoboticArm] 执行主机械臂特定初始化");

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[MainRoboticArm] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[MainRoboticArm] 拉花配置加载成功");
        }

        LOG_INFO("[MainRoboticArm] 主机械臂特定初始化完成");
        return true;
    }

    bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &/*action_params*/) override {
        LOG_INFO("[MainRoboticArm] 执行动作: {}", action_name);
        // TODO: 实现通用动作执行逻辑
        return true;
    }

private:
    LatteArtConfig latte_art_config_;
};


MainRoboticArm::MainRoboticArm() {
    impl_ = std::make_unique<Impl>();
}

MainRoboticArm::~MainRoboticArm() = default;

bool MainRoboticArm::init() {
    return impl_->init();
}

bool MainRoboticArm::shutdown() {
    return impl_->shutdown();
}

bool MainRoboticArm::emergency_stop() {
    return impl_->emergency_stop();
}

bool MainRoboticArm::execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params) {
    return impl_->execute_action(action_name, action_params);
}

} // namespace aubo
