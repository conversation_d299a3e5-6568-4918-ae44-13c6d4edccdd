/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_MILK_CONTAINER_CLEANER_H
#define AUBO_COFFEE_SERVICE_MILK_CONTAINER_CLEANER_H

#include <any>
#include <string>
#include <map>
#include <memory>

namespace aubo {

/**
 * @class MilkContainerCleaner
 * @brief 牛奶容器清洁器类
 *
 * 负责清洁牛奶容器和相关管路
 */
class MilkContainerCleaner {
public:
    /**
     * @brief 构造函数
     */
    MilkContainerCleaner();

    /**
     * @brief 析构函数
     */
    ~MilkContainerCleaner();

    /**
     * @brief 初始化清洁器
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭清洁器
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params = {});

    /**
     * @brief 获取清洁器状态
     * @return 状态信息字符串
     */
    std::string get_status() const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_MILK_CONTAINER_CLEANER_H
