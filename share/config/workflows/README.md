# 咖啡工作流配置文件

## 概述

这个目录包含了所有咖啡制作工作流的配置文件。每种咖啡都有自己独立的配置文件，便于管理和维护。

## 文件说明

### 主要配置文件

- **`americano.json`** - 美式咖啡制作流程
- **`latte.json`** - 拿铁咖啡制作流程
- **`cappuccino.json`** - 卡布奇诺制作流程
- **`mocha.json`** - 摩卡咖啡制作流程



### 单个工作流文件格式

每个工作流文件包含：
- **工作流基本信息**: name, description, coffee_type
- **详细的制作步骤**: 使用统一的steps结构
- **执行模式**: 通过mode字段支持sequential(顺序)和parallel(并行)
- **元数据**: version, created_date等

### 格式特性

- **统一结构**: 所有工作流使用相同的JSON结构
- **mode字段**: 指定执行模式，支持sequential(顺序)和parallel(并行)
- **type字段**: 明确标识每个项目的类型(group/action/wait)
- **清晰层次**: 使用嵌套的group结构组织复杂工作流

```json
{
  "workflow": {
    "name": "美式咖啡制作流程",
    "description": "简单的美式咖啡制作",
    "coffee_type": "americano",
    "steps": {
      "type": "group",
      "name": "美式咖啡制作流程",
      "execution": "sequential",
      "items": [
        {
          "type": "group",
          "name": "取杯子",
          "execution": "sequential",
          "items": [
            {
              "type": "action",
              "executor": "main_robotic_arm",
              "operation": "move_to_ready"
            },
            {
              "type": "action",
              "executor": "cup_dispenser",
              "operation": "dispense_cup"
            }
          ]
        }
      ]
    }
  },
  "metadata": {
    "version": "2.0",
    "created_date": "2025-01-16"
  }
}
```

## 工作流分类

### basic (基础咖啡)
- **americano** - 美式咖啡
- 特点：简单快速，无需牛奶

### milk_based (奶咖系列)
- **latte** - 拿铁咖啡
- **cappuccino** - 卡布奇诺
- 特点：需要牛奶和拉花技术

### specialty (特色咖啡)
- **mocha** - 摩卡咖啡
- 特点：复杂制作，需要特殊材料

## 难度等级

- **easy** - 简单 (≤120秒)
- **medium** - 中等 (≤200秒)
- **medium-hard** - 中高 (≤250秒)
- **hard** - 困难 (≤300秒)

## 使用方法

### 1. 从目录加载 (推荐)

```cpp
// 扫描目录中的所有JSON文件
coffee.load_workflows_from_directory("share/config/workflows");

// 查看可用工作流
auto workflows = coffee.get_available_workflows();
```

### 2. 加载单个工作流

```cpp
// 加载特定的工作流文件
coffee.load_single_workflow("share/config/workflows/latte.json");
```

## 自定义工作流

### 创建新工作流

1. **创建工作流文件**
   ```bash
   cp americano.json my_coffee.json
   ```

2. **编辑工作流内容**
   - 修改 `coffee_type` 为新的ID
   - 更新 `name` 和 `description`
   - 调整制作步骤

3. **重新加载工作流**
   重新运行程序，系统会自动发现新的工作流文件

### 修改现有工作流

1. **备份原文件**
   ```bash
   cp latte.json latte.json.backup
   ```

2. **编辑配置**
   - 调整步骤参数
   - 修改超时时间
   - 更新动作参数

3. **测试工作流**
   使用演示程序测试修改后的工作流

## 最佳实践

### 配置管理
- 为每种咖啡创建独立配置文件
- 使用有意义的文件名和ID
- 保持配置文件的一致性

### 参数调优
- 根据实际设备调整超时时间
- 优化并行执行策略
- 设置合适的动作参数

### 版本控制
- 使用版本控制系统管理配置文件
- 记录重要的配置变更
- 保留配置文件的备份

### 测试验证
- 在实际环境中测试新配置
- 验证所有步骤的执行
- 检查错误处理机制

## 故障排除

### 常见问题

1. **工作流加载失败**
   - 检查JSON语法是否正确
   - 确认文件路径是否存在
   - 查看日志获取详细错误

2. **步骤执行失败**
   - 检查动作名称是否正确
   - 确认参数格式是否正确
   - 调整超时时间设置

3. **注册表不匹配**
   - 确保注册表中的文件名正确
   - 检查工作流ID是否一致
   - 验证分类和难度设置

### 调试技巧

- 使用演示程序测试配置
- 启用详细日志记录
- 逐步验证每个步骤
- 检查配置文件语法

## 扩展开发

### 添加新动作
1. 在机器人类中实现新动作
2. 更新系统支持的动作列表
3. 在工作流中使用新动作

### 自定义参数
1. 定义新的参数类型
2. 在动作执行中处理参数
3. 更新配置文件示例

### 集成外部系统
1. 扩展工作流引擎
2. 添加外部接口支持
3. 实现数据交换机制

通过模块化的配置系统，您可以更灵活地管理和维护咖啡制作工作流，实现真正的可配置化自动咖啡制作系统。
