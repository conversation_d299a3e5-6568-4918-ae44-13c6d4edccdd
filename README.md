# AUBO 咖啡制作系统

基于双臂机器人的智能咖啡制作系统，支持美式咖啡、拿铁和卡布奇诺的自动制作。

## 快速开始

```cpp
#include "coffee_system.h"

int main() {
    aubo::CoffeeSystem coffee_system;

    // 初始化系统
    if (!coffee_system.initialize()) {
        return 1;
    }

    // 制作拿铁咖啡
    aubo::CoffeeOrder order("ORDER_001", aubo::CoffeeType::LATTE,
                           aubo::LatteArtType::HEART, 1);
    coffee_system.make_coffee(order);

    coffee_system.shutdown();
    return 0;
}
```

## 支持的咖啡类型

- **美式咖啡** - 单臂操作，简单高效
- **拿铁咖啡** - 双臂协调，支持心形、叶子、郁金香、天鹅拉花
- **卡布奇诺** - 双臂协调，心形拉花



## 编译和运行

```bash
mkdir build && cd build
cmake ..
make

# 运行演示程序
./bin/coffee_demo
```

## 系统架构

```mermaid
graph TD
    A[咖啡制作系统] --> B[工作流引擎]
    B --> C[主机械臂机器人]
    B --> D[副机械臂机器人]
    B --> E[咖啡机]
    B --> F[杯子分配器]
    B --> G[奶缸清洗机]
```

系统采用分层架构，咖啡制作系统作为统一接口，工作流引擎协调多个设备的协同工作。

## 工作流选择

```mermaid
flowchart TD
    A[咖啡订单] --> B{咖啡类型}

    B -->|美式| C[美式咖啡]
    B -->|拿铁| D{拉花类型}
    B -->|卡布奇诺| E[卡布奇诺-心形]

    D -->|心形| F[拿铁-心形]
    D -->|叶子| G[拿铁-叶子]
    D -->|郁金香| H[拿铁-郁金香]
    D -->|天鹅| I[拿铁-天鹅]

    C --> J[执行制作流程]
    E --> J
    F --> J
    G --> J
    H --> J
    I --> J
```